package com.thedasagroup.suminative.ui

import android.annotation.SuppressLint
import android.app.ProgressDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Picture
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.TableRestaurant
import androidx.compose.material.icons.filled.Restaurant
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SheetValue
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.list.listItems
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.launcher.MavericksLauncherActivity
import com.airbnb.mvrx.viewModel
import com.pluto.Pluto
import com.pluto.plugins.network.PlutoNetworkPlugin
import com.sumup.merchant.reader.api.SumUpAPI
import com.sumup.merchant.reader.models.TransactionInfo
import com.sunmi.printerx.enums.Align
import com.sunmi.printerx.style.BitmapStyle
import com.thedasagroup.suminative.BaseActivity
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.ThemeGreen
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.request.pagination.Customer
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.request.print.PrintBillRequest
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import com.thedasagroup.suminative.data.model.response.store_orders.convertCartsToJson
import com.thedasagroup.suminative.data.prefs.validate
import com.thedasagroup.suminative.printOrderBitmap
import com.thedasagroup.suminative.ui.lcd.LcdViewModel
import com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity
import com.thedasagroup.suminative.ui.login.LoginActivity
import com.thedasagroup.suminative.ui.login.LoginScreenViewModel
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
import com.thedasagroup.suminative.ui.orders.OrderState
import com.thedasagroup.suminative.ui.orders.PrintingPreviewDialog2
import com.thedasagroup.suminative.ui.orders.ScheduleOrderScreenTopFunction
import com.thedasagroup.suminative.ui.categories.CashPaymentCompose
import com.thedasagroup.suminative.ui.payment.PaymentViewModel
import com.thedasagroup.suminative.ui.printer.PrinterViewModel
import com.thedasagroup.suminative.ui.printer.selectPrinter
import com.thedasagroup.suminative.ui.products.cart.CartScreenFigma
import com.thedasagroup.suminative.ui.products.ProductDetailsBottomSheet
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.products.RightModalDrawer
import com.thedasagroup.suminative.ui.sales.SalesActivity
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.throttleFirst
import com.thedasagroup.suminative.ui.stock.StockActivity
import com.thedasagroup.suminative.ui.stock.StockScreenViewModel
import com.thedasagroup.suminative.ui.stores.SelectStoreActivity
import com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer
import com.thedasagroup.suminative.ui.utils.SunmiPrintHelper
import com.thedasagroup.suminative.ui.utils.formatDateToHourAndMinute
import com.thedasagroup.suminative.ui.utils.getDateFromHourAndMinute
import com.thedasagroup.suminative.ui.utils.getDayOfWeek
import com.thedasagroup.suminative.ui.utils.getMinutesBetweenTwoDates
import com.thedasagroup.suminative.ui.utils.toGMT
import com.thedasagroup.suminative.ui.utils.transformDecimal
import com.thedasagroup.suminative.work.LogUploadManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import java.util.UUID
import com.thedasagroup.suminative.ui.common.CommonState
import com.thedasagroup.suminative.ui.common.CommonViewModel
import com.thedasagroup.suminative.ui.common.CardConfirmationDialog
import com.thedasagroup.suminative.ui.common.SuccessAvailDialog
import com.thedasagroup.suminative.ui.common.SuccessDialog
import com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity
import com.thedasagroup.suminative.ui.payment.CashPaymentActivity
import com.thedasagroup.suminative.ui.payment.PaymentActivity
import com.thedasagroup.suminative.ui.payment.PaymentDialogHelper
import com.thedasagroup.suminative.ui.products.ProductsScreen
import com.thedasagroup.suminative.ui.refund.RefundSumUpActivity
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import com.thedasagroup.suminative.ui.reservations.ReservationsViewModel
import com.thedasagroup.suminative.ui.rewards.RewardsCartHelper
import com.thedasagroup.suminative.ui.rewards.RewardsDialog
import com.thedasagroup.suminative.ui.rewards.RewardsState
import com.thedasagroup.suminative.ui.rewards.RewardsViewModel
import com.thedasagroup.suminative.ui.rewards.extractBusinessId
import com.thedasagroup.suminative.ui.rewards.extractCustomerId
import com.thedasagroup.suminative.ui.service.TableUpdateSocket
import com.thedasagroup.suminative.ui.splitbill.SplitBillActivity
import com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity
import kotlinx.serialization.json.Json
import kotlin.collections.get


class MainActivity : BaseActivity(), MavericksView {

    private val TAG: String? = MainActivity::class.simpleName
    val loginViewModel: LoginScreenViewModel by viewModel()
    val orderScreenViewModel: OrderScreenViewModel by viewModel()
    val reservationsViewModel: ReservationsViewModel by viewModel()
    val productsScreenViewModel: ProductsScreenViewModel by viewModel()
    val stockScreenViewModel: StockScreenViewModel by viewModel()
    val paymentViewModel: PaymentViewModel by viewModel()
    val commonViewModel: CommonViewModel by viewModel()

    val rewardsViewModel: RewardsViewModel by viewModel()

    private var AL: ArrayList<HashMap<String, Any>> = ArrayList()

    var dialog: MaterialDialog? = null
    var isShowAllOrders: Boolean = false
    var ordersResponse: OrderResponse? = null
    var mapCount: Map<Int?, Int> = mutableMapOf()

    var dialogType: String? = null
    var dialogMessage: String? = null
    var isScheduleOrder: Boolean = false
    var showDialogProp: String? = null
    var dialogTitle: String? = null
    var currentRouteId: String = "0"

    var productState: ProductsScreenState? = null

    // Store current order for SumUp payment flow
    private var currentSumUpOrder: Order? = null

    // Processing dialog for order saving
    private var processingDialog: MaterialDialog? = null

    // Store QR code scan result for rewards
    private var qrCodeScanResult: String? = null

    var displays: Array<Display> = arrayOf()

    lateinit var progressDialog : ProgressDialog

    // Function to start SumUp payment flow
    fun startSumUpPayment(order: Order) {
        currentSumUpOrder = order
        if (SumUpPaymentHelper.isLoggedIn()) {
            // Already logged in, start payment directly
            SumUpPaymentHelper.startPayment(this, order)
        } else {
            // Not logged in, start login first
            SumUpPaymentHelper.startLogin(this)
        }
    }

    // Show processing dialog
    private fun showProcessingDialog() {
        processingDialog?.dismiss()
        processingDialog = MaterialDialog(this).show {
            title(text = "Processing Order")
            message(text = "Please wait while we save your order...")
            cancelable(false)
            cancelOnTouchOutside(false)
        }
    }

    // Hide processing dialog
    private fun hideProcessingDialog() {
        processingDialog?.dismiss()
        processingDialog = null
    }

    private val printerViewModel: PrinterViewModel by viewModels()
    private val lcdViewModel: LcdViewModel by viewModels()

    private val cashPaymentLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        paymentViewModel.showChangeDialog(showChangeDialog = false)

        if (result.resultCode == RESULT_OK) {
            val completedOrder =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    result.data?.getParcelableExtra(
                        CashPaymentActivity.RESULT_ORDER,
                        Order::class.java
                    )
                } else {
                    @Suppress("DEPRECATION")
                    result.data?.getParcelableExtra(CashPaymentActivity.RESULT_ORDER)
                }

            completedOrder?.let { finalOrder ->
                handleCashPaymentCompleteGuava(finalOrder)
            }
        }
    }

    private val cardPaymentLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val completedOrder =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    result.data?.getParcelableExtra(
                        PaymentActivity.RESULT_ORDER,
                        Order::class.java
                    )
                } else {
                    @Suppress("DEPRECATION")
                    result.data?.getParcelableExtra(PaymentActivity.RESULT_ORDER)
                }

            completedOrder?.let { finalOrder ->
                handleCashPaymentCompleteGuava(finalOrder)
            }
        }
    }

    // Activity result launcher for assign table functionality
    val assignTableLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val selection = AreaTableSelectionHelper.parseResult(result.resultCode, result.data)
        if (selection != null) {
            // Assign global cart items to the selected table
//            productsScreenViewModel.assignGlobalCartToTable(selection)
            // Navigate to Store Items tab
            orderScreenViewModel.updateCurrentRoute("0")
        }
    }


    val sumniQRCodeScanLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            val bundle: Bundle = data!!.getExtras()!!
            val result = bundle.getSerializable("data") as ArrayList<*>?
            val it: MutableIterator<*> = result!!.iterator()
            var scannedData: String = ""
            while (it.hasNext()) {
                val hashMap = it.next() as HashMap<*, *>
                Log.i("sunmi", (hashMap.get("TYPE") as String?) ?: "") //Scan type
                Log.i("sunmi", (hashMap.get("VALUE") as String?) ?: "") //Scan result
                scannedData = (hashMap["VALUE"] as String?) ?: ""
            }
            scannedData?.let { scannedData ->
                // Pass the QR code result to rewards screen
                handleQRCodeScanResult(scannedData)
            }
        }
    }

    private fun handleQRCodeScanResult(scannedData: String) {
        // Store the QR code result
        qrCodeScanResult = scannedData

        // Extract customer ID from QR code (assuming format like "dasa-businessId-customerId")
        val customerId = extractCustomerId(scannedData)
        val businessId = extractBusinessId(scannedData)

        if (customerId != null) {
            // Set the customer ID input in rewards view model
            rewardsViewModel.updateCustomerIdInput(scannedData)

            // Automatically search for the customer
            lifecycleScope.launch {
                rewardsViewModel.getAllCustomers(
                    customerId = customerId,
                    businessId = businessId ?: productsScreenViewModel.prefs?.store?.businessId ?: 0
                )
            }
        }

        // Show rewards dialog with the scanned data
        showRewardsDialog()
    }

    private fun showRewardsDialog() {
        rewardsViewModel.showRewardsDialog(show = true)
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @SuppressLint("CheckResult")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        /*val logLevel = LogLevel.VERBOSE

        // Create your token provider.
        val tokenProvider = CustomConnectionTokenProvider(
            stockRepository = stockScreenViewModel.stockRepository, coroutineScope = lifecycleScope
        )

        // Pass in the current application context, your desired logging level, your token provider, and the listener you created
        if (!Terminal.isInitialized()) {
            Terminal.initTerminal(applicationContext, logLevel, tokenProvider, listener)
        }*/
        progressDialog = ProgressDialog(this)
        lifecycleScope.launch(Dispatchers.Main) {
            orderScreenViewModel.onEach(OrderState::isShowAllOrders) {
                isShowAllOrders = it
            }
            orderScreenViewModel.onEach(OrderState::ordersResponse) { response ->
                if (response is Success) {
                    ordersResponse = response()
                }
            }
            orderScreenViewModel.onEach(OrderState::mapCount) {
                mapCount = it
            }
            orderScreenViewModel.onEach(OrderState::dialogType) {
                dialogType = it
            }
            orderScreenViewModel.onEach(OrderState::dialogMessage) {
                dialogMessage = it
            }
            orderScreenViewModel.onEach(OrderState::currentRouteId) {
                currentRouteId = it
            }
            orderScreenViewModel.onEach(OrderState::isScheduleOrder) {
                isScheduleOrder = it
            }
            orderScreenViewModel.onEach(OrderState::dialogTitle) { title ->
                if (title.isNotEmpty()) {
                    if (dialogType == "update_order_with_dialog") {
                        if (dialog?.isShowing != true) {
                            showAutoDismissDialog(
                                message = dialogMessage ?: "Update Order", title = "Update Order"
                            )
                        } else {
                            dialog?.dismiss()
                            showAutoDismissDialog(
                                message = dialogMessage ?: "Update Order",
                                title = "Update Order",
                            )
                        }
                    } else {
                        if (dialog?.isShowing != true) {
                            showDialog(
                                message = dialogMessage ?: "New Order",
                                title = dialogTitle ?: "New Order",
                                type = dialogType ?: "new_order",
                                isScheduleOrder = isScheduleOrder
                            )
                        } else {
                            dialog?.dismiss()
                            showDialog(
                                message = dialogMessage ?: "New Order",
                                title = dialogTitle ?: "New Order",
                                type = dialogType ?: "new_order",
                                isScheduleOrder = isScheduleOrder
                            )
                        }
                    }
                } else {
                    dialog?.dismiss()
                }
            }
        }

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mMessageReceiver, IntentFilter("custom-event-name"))

        if (loginViewModel.prefs.loginResponse?.validate() == true) {
            setContent {
                SumiNativeTheme {
                    val coroutineScope = rememberCoroutineScope()
                    val order by productsScreenViewModel.collectAsState { it.getCurrentTableOrder() }
                    val quantity by productsScreenViewModel.collectAsState(ProductsScreenState::stock)
                    val showCart by productsScreenViewModel.collectAsState(ProductsScreenState::showCart)
                    val isShowPrintingPreviewDialog by productsScreenViewModel.collectAsState(
                        ProductsScreenState::isShowPrintingPreview
                    )
                    val selectedTableIndex by productsScreenViewModel.collectAsState(
                        ProductsScreenState::selectedTableIndex
                    )

                    val showRewardsDialog by rewardsViewModel.collectAsState(RewardsState::showRewardsDialog)

                    val snackbarHostState = remember { SnackbarHostState() }

                    if (isShowPrintingPreviewDialog != null) {
                        PrintingPreviewDialog2(
                            order = isShowPrintingPreviewDialog!!,
                            onPrintBill = { bitmap ->
                                if(productsScreenViewModel.prefs.storeConfigurations?.data?.cloudPrintersEnabledDefault == true || BuildConfig.DEBUG){
                                    coroutineScope.launch (Dispatchers.IO){
                                        withContext(Dispatchers.Main){
                                            progressDialog.setMessage("Printing...")
                                            progressDialog.show()
                                        }
                                        val cart = isShowPrintingPreviewDialog?.order?.getCart()
                                        val updatedCart = cart?.map {
                                            it.copy(
                                                orderNumber = isShowPrintingPreviewDialog?.order?.id
                                            )
                                        }
                                        productsScreenViewModel.printBill(request = PrintBillRequest(
                                            storeId = productsScreenViewModel.prefs.store?.id ?: -1,
                                            cartJson = convertCartsToJson(
                                                carts = updatedCart ?: mutableListOf()
                                            ),
                                            serviceCharge = productState?.isServiceChargeApplied() ?: false,
                                            tableName = if(productState?.getCurrentTableId() != null){
                                                productState?.selectedTableIndex?.let {index ->
                                                    if(index >= 0 && index < (productState?.selectedTables?.size
                                                            ?: 0)
                                                    ){
                                                        productState?.selectedTables[index]?.tableName
                                                    }else{
                                                        "Table-${productState?.getCurrentTableId()}"
                                                    }
                                                }
                                            }else{
                                                "Walk-in"
                                            }
                                        )
                                        ).collectLatest { response ->
                                            when(response){
                                                is Success<*> ->{
                                                    withContext(Dispatchers.Main){
                                                        commonViewModel.updateShowSuccessDialog(true)
                                                        commonViewModel.updateSuccessDialogMessage("Order Printed Successfully")
                                                        progressDialog.hide()
                                                        productsScreenViewModel.updateShowPrintingPreview(
                                                            null, shouldPrintInstant = false
                                                        )
                                                        coroutineScope.launch {
                                                            productsScreenViewModel.updateOrder(Order())
                                                        }
                                                    }
                                                }
                                                is Fail -> {
                                                    withContext(Dispatchers.Main) {
                                                        commonViewModel.updateShowSuccessDialog(true)
                                                        commonViewModel.updateSuccessDialogMessage("Order Printing Failed")
                                                        progressDialog.hide()
                                                    }
                                                }
                                                is Loading<*> -> {
                                                    withContext(Dispatchers.Main) {
                                                        progressDialog.show()
                                                    }
                                                }
                                                else -> {

                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    printOrderBitmap(bitmap, this)
                                    productsScreenViewModel.updateShowPrintingPreview(
                                        null, shouldPrintInstant = false
                                    )
                                }
                            },
                            onCancel = {
                                productsScreenViewModel.updateShowPrintingPreview(
                                    null, shouldPrintInstant = false
                                )
                            },
                            productsScreenViewModel = productsScreenViewModel
                        )
                    }

                    RightModalDrawer {

                        val state by productsScreenViewModel.collectAsState()

                        val isBottomSheetVisible by productsScreenViewModel.collectAsState(
                            ProductsScreenState::isBottomSheetVisible
                        )

                        val sheetState = rememberModalBottomSheetState(
                            skipPartiallyExpanded = true,
                            confirmValueChange = { it != SheetValue.Hidden })

                        val isCartVisible =
                            if (state.selectedTables.isNotEmpty()) showCart && state.selectedTableIndex >= 0 else showCart

                        val showSuccessDialog by commonViewModel.collectAsState(CommonState::showSuccessDialog)
                        val successDialogMessage by commonViewModel.collectAsState(CommonState::successDialogMessage)
                        val showSuccessAvailDialog by commonViewModel.collectAsState(CommonState::showSuccessAvailDialog)
                        val successDialogAvailMessage by commonViewModel.collectAsState(CommonState::successDialogAvailMessage)
                        val showCardPaymentConfirmDialog by commonViewModel.collectAsState(
                            CommonState::showCardPaymentConfirmDialog
                        )
                        val selectedTables by productsScreenViewModel.collectAsState(
                            ProductsScreenState::selectedTables
                        )
                        val selectedTableIndex by productsScreenViewModel.collectAsState(
                            ProductsScreenState::selectedTableIndex
                        )
                        val tableOrders by productsScreenViewModel.collectAsState(
                            ProductsScreenState::tableOrders
                        )
                        val availableCourses by productsScreenViewModel.collectAsState { it.getCurrentTableAvailableCourses() }
                        val cartItemsWithCourses by productsScreenViewModel.collectAsState(
                            ProductsScreenState::cartItemsWithCourses
                        )
                        val globalCartItemsWithCourses by productsScreenViewModel.collectAsState(
                            ProductsScreenState::globalCartItemsWithCourses
                        )

                        if (isCartVisible) {
                            orderScreenViewModel.updateCurrentRoute(currentRoute = "0")
                            // When cart is visible, show split layout with topbar in left column
                            Row(modifier = Modifier.fillMaxSize()) {
                                // Left column with topbar and products
                                Column(
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .fillMaxWidth(0.6f)
                                ) {
                                    Scaffold(
                                        modifier = Modifier.fillMaxSize(),
                                        topBar = {
                                            MyTopAppBar(viewModel = orderScreenViewModel)
                                        },
                                        snackbarHost = { SnackbarHost(snackbarHostState) }
                                    ) { innerPadding ->
                                        ProductsScreen(
                                            viewModel = productsScreenViewModel,
                                            onBackClick = {},
                                            onOpenProductDetails = { stItem ->
                                                // When launching from products grid, create a temporary cart wrapper
                                                productsScreenViewModel.updateProductDetailsBottomSheetVisibility(
                                                    Cart(
                                                        quantity = 1,
                                                        storeItem = stItem.toStoreItem(),
                                                        netPayable = stItem.price,
                                                        price = stItem.price,
                                                        tax = stItem.tax
                                                    )
                                                )
                                            },
                                            openCart = {
                                                coroutineScope.launch {
                                                    productsScreenViewModel.updateCartVisibility(
                                                        true
                                                    )
                                                }
                                            },
                                            stockScreenViewModel = stockScreenViewModel,
                                            orderScreenViewModel = orderScreenViewModel,
                                            reservationsViewModel = reservationsViewModel,
                                            mainActivity = this@MainActivity,
                                            addDirectlyToCart = { stockItem ->
                                                coroutineScope.launch {
                                                    val isAdded = productsScreenViewModel.addItemToCart(
                                                        order = order,
                                                        storeItem = stockItem.toStoreItem(),
                                                        quantity = 1,
                                                        optionDetails = OptionDetails(),
                                                        selectedTables = selectedTables,
                                                        selectedTableIndex = selectedTableIndex,
                                                        tableOrders = tableOrders,
                                                        state = state
                                                    )
                                                    productsScreenViewModel.updateCartVisibility(
                                                        visible = true
                                                    )
                                                    if (isAdded) {
                                                        // Show success dialog for item added
                                                        commonViewModel.updateShowSuccessDialog(true)
                                                        commonViewModel.updateSuccessDialogMessage("Item added to cart")
                                                    }

                                                    withContext(Dispatchers.Main) {
                                                        if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                                                            lcdViewModel.lcdDigital(
                                                                order.totalPrice(
                                                                    applyServiceCharge = state.serviceChargeApplied,
                                                                    serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
                                                                )?.transformDecimal()
                                                                    ?: ""
                                                            )
                                                        }
                                                    }
                                                }
                                            },
                                            onTableSelected = {
                                                coroutineScope.launch {
                                                    productsScreenViewModel.updateCartVisibility(
                                                        visible = true
                                                    )
                                                }
                                            },
                                            onRemoveCustomer = {
                                                rewardsViewModel.resetState()
                                                productsScreenViewModel.clearCurrentCustomer()
                                            },
                                            onAddNewTableClick = {
                                                coroutineScope.launch(Dispatchers.IO) {
                                                    orderScreenViewModel.updateCurrentRoute("1")
                                                }
                                            },
                                            onTableRemove = { tableId ->
                                                productsScreenViewModel.clearCartAndRemoveTable(
                                                    state = state,
                                                    currentTableId = tableId
                                                )
                                            },
                                            onGoToPayTab = { tableId ->
                                                coroutineScope.launch(Dispatchers.IO) {
                                                    productsScreenViewModel.openPayTabForTable(
                                                        tableId = tableId,
                                                        state = state,
                                                        onOpenCart = {
                                                            coroutineScope.launch {
                                                                productsScreenViewModel.updateCartVisibility(
                                                                    visible = true
                                                                )
                                                            }
                                                        }
                                                    )
                                                }
                                            }
                                        )
                                    }
                                }

                                // Right column with full height cart
                                Column(
                                    modifier = Modifier
                                        .weight(0.4f)
                                        .fillMaxSize()
                                ) {
                                    CartScreenFigma(
                                        order = order,
                                        state = state,
                                        onCrossClick = {
                                            coroutineScope.launch {
                                                productsScreenViewModel.updateCartVisibility(visible = false)
                                            }
                                        },
                                        onRemoveItem = { cartItem ->
                                            coroutineScope.launch {
                                                productsScreenViewModel.removeItemFromCart(
                                                    order = order,
                                                    cartItem = cartItem,
                                                    selectedTables = selectedTables,
                                                    selectedTableIndex = selectedTableIndex,
                                                    tableOrders = tableOrders,
                                                    currentState = state
                                                ).collectLatest { updatedOrder ->
                                                    if (updatedOrder is Success) {
                                                        // Show success dialog for item removed
                                                        commonViewModel.updateShowSuccessDialog(show = true)
                                                        commonViewModel.updateSuccessDialogMessage("Item removed from cart")

                                                        withContext(Dispatchers.Main) {
                                                            if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                                                                lcdViewModel.lcdDigital(
                                                                    updatedOrder().totalPrice(
                                                                        applyServiceCharge = state.serviceChargeApplied,
                                                                        serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
                                                                    )?.transformDecimal()
                                                                        ?: ""
                                                                )
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        closeCart = {
                                            coroutineScope.launch {
                                                productsScreenViewModel.updateCartVisibility(visible = false)
                                            }
                                        },
                                        onUpdateStock = { stock, storeItem, cart, optionDetails ->
                                            productsScreenViewModel.updateCartStock(
                                                order = order,
                                                stock = stock,
                                                stockItem = storeItem,
                                                optionDetails = optionDetails ?: OptionDetails(),
                                                selectedTables = selectedTables,
                                                selectedTableIndex = selectedTableIndex,
                                                tableOrders = tableOrders,
                                                state = state,
                                                cart = cart
                                            )
                                        },
                                        onUpdateNotes = { cart, notes ->
                                            productsScreenViewModel.updateCartItemNotes(
                                                order = order,
                                                cart = cart,
                                                notes = notes,
                                                selectedTableIndex = selectedTableIndex,
                                                selectedTables = selectedTables,
                                                tableOrders = tableOrders,
                                                state = state
                                            )
                                        },
                                        onVoidItem = { cart ->
                                            productsScreenViewModel.voidCartItem(
                                                order = order,
                                                cart = cart,
                                                selectedTables = selectedTables,
                                                selectedTableIndex = selectedTableIndex,
                                                tableOrders = tableOrders
                                            )
                                        },
                                        placeOrderCash = {total ->
                                            if (order.carts?.isNotEmpty() == true) {
                                                val updatedOrder = order.copy(paymentType = 5,
                                                    totalPrice = total
                                                )
                                                paymentViewModel.showChangeDialog(showChangeDialog = true)
                                                CashPaymentCompose.showCashPaymentActivity(
                                                    activity = this@MainActivity,
                                                    order = updatedOrder,
                                                    launcher = cashPaymentLauncher
                                                )
                                            }
                                        },
                                        placeOrderCard = {
                                            if (order.carts?.isNotEmpty() == true) {
                                                // Show confirmation dialog first
                                                commonViewModel.updateShowCardPaymentConfirmDialog(
                                                    true
                                                )
                                            }
                                        },
                                        onSplitBillClick = { numberOfPersons ->
                                            if (order.carts?.isNotEmpty() == true) {
                                                val intent = SplitBillActivity.createIntent(
                                                    context = this@MainActivity,
                                                    order = order,
                                                    numberOfPersons = numberOfPersons
                                                )
                                                startActivity(intent)
                                            }
                                        },
                                        onCloudPrintClick = {
                                            coroutineScope.launch {
                                                productsScreenViewModel.cloudPrint(
                                                    request = CloudPrintRequest(
                                                        storeId = (productsScreenViewModel.prefs?.store?.id
                                                            ?: 0).toString(),
                                                        carts = order.carts ?: mutableListOf()
                                                    )
                                                ).collectLatest { response ->
                                                    if (response is Success) {
                                                        commonViewModel.updateShowSuccessDialog(true)
                                                        commonViewModel.updateSuccessDialogMessage("Order printed successfully")
                                                        withContext(Dispatchers.Main) {
                                                            if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                                                                lcdViewModel.lcdDigital(
                                                                    order.totalPrice(
                                                                        applyServiceCharge = state.serviceChargeApplied,
                                                                        serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
                                                                    )?.transformDecimal()
                                                                        ?: ""
                                                                )
                                                            }
                                                        }
                                                    } else if (response is Fail) {
                                                        commonViewModel.updateShowSuccessDialog(true)
                                                        commonViewModel.updateSuccessDialogMessage("Failed to print order: ${response.error.message}")
                                                    }
                                                }
                                            }
                                        },
                                        onAddNewCourse = { newCourse ->
                                            coroutineScope.launch {
                                                productsScreenViewModel.addNewCourse(
                                                    courseName = newCourse,
                                                    availableCourses = availableCourses
                                                )
                                            }
                                        },
                                        onCourseBillClick = {
                                            coroutineScope.launch(Dispatchers.IO) {
                                                productsScreenViewModel.printBill(
                                                    selectedTableIndex = selectedTableIndex,
                                                    selectedTables = selectedTables,
                                                    cartItemsWithCourses = cartItemsWithCourses,
                                                    availableCourses = availableCourses,
                                                    globalCartItemsWithCourses = globalCartItemsWithCourses,
                                                    state = state
                                                ).collectLatest { result ->
                                                    when (result) {
                                                        is Success -> {
                                                            withContext(Dispatchers.Main) {
                                                                commonViewModel.updateShowSuccessDialog(
                                                                    show = true
                                                                )
                                                                commonViewModel.updateSuccessDialogMessage(
                                                                    "Print Bill Successfully"
                                                                )
                                                            }
                                                        }

                                                        is Fail -> {
                                                            withContext(Dispatchers.Main) {
                                                                commonViewModel.updateShowSuccessDialog(
                                                                    show = true
                                                                )
                                                                commonViewModel.updateSuccessDialogMessage(
                                                                    result.error?.message
                                                                        ?: "Could not print"
                                                                )
                                                            }
                                                        }

                                                        else -> {

                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        onGoButtonClick = { courseId ->
                                            coroutineScope.launch(Dispatchers.IO) {
                                                productsScreenViewModel.sendCoursesNotificationForCourse(
                                                    courseId = courseId,
                                                    selectedTables = selectedTables,
                                                    selectedTableIndex = selectedTableIndex,
                                                    cartItemsWithCourses = cartItemsWithCourses,
                                                    globalCartItemsWithCourses = globalCartItemsWithCourses,
                                                    availableCourses = availableCourses,
                                                    state = state
                                                ).collectLatest { result ->
                                                    when (result) {
                                                        is Success -> {
                                                            withContext(Dispatchers.Main) {
                                                                commonViewModel.updateShowSuccessDialog(
                                                                    show = true
                                                                )
                                                                commonViewModel.updateSuccessDialogMessage(
                                                                    "Course Print Sent Successfully"
                                                                )
                                                            }
                                                        }

                                                        is Fail -> {
                                                            withContext(Dispatchers.Main) {
                                                                commonViewModel.updateShowSuccessDialog(
                                                                    show = true
                                                                )
                                                                commonViewModel.updateSuccessDialogMessage(
                                                                    result.error?.message
                                                                        ?: "Could not print"
                                                                )
                                                            }
                                                        }

                                                        else -> {

                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        onCompleteButtonClick = { courseId ->
                                            coroutineScope.launch {
                                                productsScreenViewModel.markCourseAsComplete(
                                                    courseId,
                                                    state
                                                )
                                                withContext(Dispatchers.Main) {
                                                    commonViewModel.updateShowSuccessDialog(show = true)
                                                    commonViewModel.updateSuccessDialogMessage("Course marked as complete")
                                                }
                                            }
                                        },
                                        onSendToKitchen = {
                                            coroutineScope.launch {
                                                productsScreenViewModel.sendToKitchen(
                                                    selectedTableIndex = selectedTableIndex,
                                                    selectedTables = selectedTables,
                                                    cartItemsWithCourses = cartItemsWithCourses,
                                                    globalCartItemsWithCourses = globalCartItemsWithCourses,
                                                    state = state
                                                ).collectLatest { result ->
                                                    when (result) {
                                                        is Success -> {
                                                            withContext(Dispatchers.Main) {
                                                                commonViewModel.updateShowSuccessDialog(
                                                                    show = true
                                                                )
                                                                commonViewModel.updateSuccessDialogMessage(
                                                                    "Sent to Kitchen Successfully"
                                                                )
                                                            }
                                                        }

                                                        is Fail -> {
                                                            withContext(Dispatchers.Main) {
                                                                commonViewModel.updateShowSuccessDialog(
                                                                    show = true
                                                                )
                                                                commonViewModel.updateSuccessDialogMessage(
                                                                    result.error?.message
                                                                        ?: "Could not sent to kitchen"
                                                                )
                                                            }
                                                        }

                                                        else -> {

                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        onApplyServiceChargeClick = {
                                            productsScreenViewModel.applyServiceCharge(state = state)
                                        },
                                        onRemoveServiceChargeClick = {
                                            productsScreenViewModel.removeServiceCharge(state = state)
                                        },
                                        productsScreenViewModel = productsScreenViewModel,
                                        onAddNumberedCourse = {
                                            productsScreenViewModel.addNumberedCourse(state = state)
                                        },
                                        onSyncTable = {
                                            coroutineScope.launch(Dispatchers.IO) {
                                                productsScreenViewModel.syncTable(
                                                    state = state,
                                                    tableId = state.getCurrentTableId() ?: -1
                                                )
                                            }
                                        },
                                        onUpdateCartTab = {
                                            productsScreenViewModel.updateSelectedCartTab(
                                                cartTab = it,
                                                state = state,
                                                currentTableId = state.getCurrentTableId()
                                            )
                                        },
                                        openPrefillFromCart = { cartItem ->
                                            if (!cartItem.sentToKitchen) {
                                                coroutineScope.launch {
                                                    productsScreenViewModel.updateProductDetailsBottomSheetVisibility(
                                                        cartItem
                                                    )
                                                }
                                            }
                                        }
                                    )
                                }
                            }
                        } else {
                            orderScreenViewModel.updateCurrentRoute(currentRoute = "0")
                            // When cart is not visible, show full screen with topbar
                            Scaffold(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .nestedScroll(rememberNestedScrollInteropConnection()),
                                snackbarHost = { SnackbarHost(snackbarHostState) },
                                topBar = { MyTopAppBar(viewModel = orderScreenViewModel) }
                            ) { innerPadding ->
                                ProductsScreen(
                                    viewModel = productsScreenViewModel,
                                    onBackClick = {},
                                    onOpenProductDetails = { stItem ->
                                        productsScreenViewModel.updateProductDetailsBottomSheetVisibility(
                                            Cart(
                                                quantity = 1,
                                                storeItem = stItem.toStoreItem(),
                                                netPayable = stItem.price,
                                                price = stItem.price,
                                                tax = stItem.tax
                                            )
                                        )
                                    },
                                    openCart = {
                                        coroutineScope.launch {
                                            productsScreenViewModel.updateCartVisibility(true)
                                        }
                                    },
                                    stockScreenViewModel = stockScreenViewModel,
                                    orderScreenViewModel = orderScreenViewModel,
                                    reservationsViewModel = reservationsViewModel,
                                    mainActivity = this@MainActivity,
                                    addDirectlyToCart = { stockItem ->
                                        coroutineScope.launch {
                                            val isAdded = productsScreenViewModel.addItemToCart(
                                                order = order,
                                                storeItem = stockItem.toStoreItem(),
                                                quantity = 1,
                                                optionDetails = OptionDetails(),
                                                selectedTables = selectedTables,
                                                selectedTableIndex = selectedTableIndex,
                                                tableOrders = tableOrders,
                                                state = state
                                            )
                                            productsScreenViewModel.updateCartVisibility(visible = true)
                                            if (isAdded) {
                                                // Show success dialog for item added
                                                commonViewModel.updateShowSuccessDialog(show = true)
                                                commonViewModel.updateSuccessDialogMessage(message = "Item added to cart")
                                            }
                                            withContext(Dispatchers.Main) {
                                                if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                                                    lcdViewModel.lcdDigital(
                                                        order.totalPrice(
                                                            applyServiceCharge = state.serviceChargeApplied,
                                                            serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
                                                        )?.transformDecimal() ?: ""
                                                    )
                                                }
                                            }
                                        }
                                    },
                                    onTableSelected = {
                                        coroutineScope.launch {
                                            productsScreenViewModel.updateCartVisibility(visible = true)
                                        }
                                    },
                                    onRemoveCustomer = {
                                        rewardsViewModel.resetState()
                                        productsScreenViewModel.clearCurrentCustomer()
                                    },
                                    onTableRemove = { tableId ->
                                        productsScreenViewModel.clearCartAndRemoveTable(
                                            state = state,
                                            currentTableId = tableId
                                        )
                                    },
                                    onAddNewTableClick = {
                                        coroutineScope.launch(Dispatchers.IO) {
                                            orderScreenViewModel.updateCurrentRoute("1")

                                        }
                                    },
                                    onGoToPayTab = { tableId ->
                                        coroutineScope.launch(Dispatchers.IO) {
                                            productsScreenViewModel.openPayTabForTable(
                                                tableId = tableId,
                                                state = state,
                                                onOpenCart = {
                                                    coroutineScope.launch {
                                                        productsScreenViewModel.updateCartVisibility(
                                                            visible = true
                                                        )
                                                    }
                                                }
                                            )
                                        }
                                    }
                                )
                            }
                        }

                        ProductDetailsBottomSheet(
                            isBottomSheetVisible = isBottomSheetVisible != null,
                            sheetState = sheetState,
                            onDismiss = {
                                coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
//                                    productsScreenViewModel.clearPrefillFromCart()
                                    productsScreenViewModel.updateProductDetailsBottomSheetVisibility(
                                        null
                                    )
                                }
                            },
                            cart = isBottomSheetVisible ?: Cart(),
                            viewModel = productsScreenViewModel,
                            updateStock = { stock, optionDetails, stockItem ->
                                productsScreenViewModel.updateStock(
                                    order = order,
                                    stock = stock,
                                    optionDetails = optionDetails,
                                    stockItem = stockItem,
                                    selectedTables = selectedTables,
                                    selectedTableIndex = selectedTableIndex,
                                    tableOrders = tableOrders,
                                    state = state
                                )
                            },
                            addToCart = { stockItem, optionDetails, appliedPrefill, isEditing ->
                                coroutineScope.launch {
                                    if (isEditing) {
                                        // update item to cart
                                        productsScreenViewModel.updateItemInCart(
                                            order = order,
                                            storeItem = appliedPrefill?.storeItem ?: StoreItem(),
                                            quantity = quantity,
                                            cart = appliedPrefill ?: Cart(),
                                            selectedTables = selectedTables,
                                            selectedTableIndex = selectedTableIndex,
                                            tableOrders = tableOrders,
                                            state = state
                                        )
                                        commonViewModel.updateShowSuccessDialog(true)
                                        commonViewModel.updateSuccessDialogMessage("Item updated in cart")
                                    } else {
                                        // Add new item to cart
                                        val isAdded = productsScreenViewModel.addItemToCart(
                                            order = order,
                                            storeItem = stockItem,
                                            quantity = quantity,
                                            optionDetails = optionDetails,
                                            selectedTables = selectedTables,
                                            selectedTableIndex = selectedTableIndex,
                                            tableOrders = tableOrders,
                                            state = state
                                        )
                                        if(isAdded) {
                                            commonViewModel.updateShowSuccessDialog(true)
                                            commonViewModel.updateSuccessDialogMessage("Item added to cart")
                                        }
                                    }
                                    withContext(Dispatchers.IO) { delay(100) }
                                    productsScreenViewModel.updateProductDetailsBottomSheetVisibility(
                                        null
                                    )
                                    productsScreenViewModel.updateCartVisibility(true)
                                    withContext(Dispatchers.Main) {
                                        if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                                            lcdViewModel.lcdDigital(
                                                order.totalPrice(
                                                    applyServiceCharge = state.serviceChargeApplied,
                                                    serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
                                                )?.transformDecimal() ?: ""
                                            )
                                        }
                                    }
                                }
                            },
                            selectOptionCondition2 = { selectedOption, isChecked, optionSet, optionDetails, stockItem ->
                                if (selectedOption != null) {
                                    if (isChecked) {
                                        productsScreenViewModel.addSelectedOptionCondition2(
                                            optionDetails = optionDetails,
                                            option = selectedOption,
                                            currentOptionSet = optionSet,
                                            stockItem = stockItem,
                                            stock = quantity,
                                            state = state
                                        )
                                    } else {
                                        productsScreenViewModel.removeSelectedOptionCondition2(
                                            optionDetails = optionDetails,
                                            selectedOption,
                                            currentOptionSet = optionSet,
                                            stockItem = stockItem,
                                            stock = quantity,
                                            state = state
                                        )
                                    }
                                }
                            },
                            selectOptionCondition1 = { selectedOption, optionSet, optionDetails, stockItem ->
                                productsScreenViewModel.updateSelectedOptionCondition1(
                                    option = selectedOption ?: Option(),
                                    optionDetails = optionDetails,
                                    stock = quantity,
                                    stockItem = stockItem,
                                    currentOptionSet = optionSet,
                                    state = state
                                )
                            })

                        // Success Dialog
                        SuccessDialog(
                            isVisible = showSuccessDialog,
                            message = successDialogMessage,
                            onDismiss = {
                                commonViewModel.updateShowSuccessDialog(false)
                            }
                        )

                        SuccessAvailDialog(
                            message = successDialogAvailMessage,
                            isVisible = showSuccessAvailDialog,
                            onDismiss = {
                                commonViewModel.updateShowSuccessAvailDialog(show = false)
                            }
                        )

                        // Card Payment Confirmation Dialog
                        CardConfirmationDialog(
                            message = "Proceed with card payment?",
                            isVisible = showCardPaymentConfirmDialog,
                            onDismiss = {
                                commonViewModel.updateShowCardPaymentConfirmDialog(show = false)
                            },
                            onContinue = {
                                // Proceed with card payment
                                commonViewModel.updateShowCardPaymentConfirmDialog(show = false)
                                if (order.carts?.isNotEmpty() == true) {
                                    handleCardPayment(order, state)
                                }
                            },
                            onGoBack = {
                                // Go back to cart
                                commonViewModel.updateShowCardPaymentConfirmDialog(show = false)
                            }
                        )

                        val currentCustomer = state.getCurrentCustomer()
                        if (currentCustomer != null) {
                            SelectedCustomerBanner(
                                customerName = currentCustomer.name ?: "Unknown Customer",
                                onRemove = {
                                    rewardsViewModel.resetState()
                                    productsScreenViewModel.clearCurrentCustomer()
                                }
                            )
                        }

                        if (showRewardsDialog) {
                            RewardsDialog(
                                viewModel = rewardsViewModel,
                                onDismiss = {
                                    rewardsViewModel.showRewardsDialog(show = false)
                                },
                                onAddToCart = { rewardItem ->
                                    // Add reward item to cart with zero price
                                    RewardsCartHelper.addRewardItemToCart(
                                        rewardItem = rewardItem,
                                        productsViewModel = productsScreenViewModel,
                                        state = state
                                    )

                                    commonViewModel.updateShowSuccessAvailDialog(true)
                                    commonViewModel.updateSuccessAvailDialogMessage("${rewardItem.storeItem?.name} - Added To Cart")
                                },
                                onStartQRScan = {
                                    startQRCodeScan()
                                },
                                onAddPointsClick = {
                                    rewardsViewModel.showRewardsDialog(show = false)
                                },
                                modifier = Modifier.fillMaxSize()
                            )
                        }

                    }
                }
            }
        } else {
            val intent = Intent(this, LoginActivity::class.java)
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }

        if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
            initPrinter()
        }

        productsScreenViewModel.onEach {
            productState = it
        }

        rewardsViewModel.onEach { rewardsState ->
            // Link the selected customer (or null if cleared) to the current table
            productsScreenViewModel.linkRewardsCustomer(rewardsState.selectedCustomer)
        }
    }

    override fun onResume() {
        super.onResume()
        showDialogWhilePlaying()

        /*if (ContextCompat.checkSelfPermission(
                this, android.Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            val permissions = arrayOf(android.Manifest.permission.ACCESS_FINE_LOCATION)
            ActivityCompat.requestPermissions(this, permissions, 129)
        }*/

        /*if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            pushNotificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }*/
        lifecycleScope.launch(Dispatchers.IO) {
            productsScreenViewModel.getStockItems()
            stockScreenViewModel.getStockItems()
            orderScreenViewModel.getOrders(false)
        }
        SunmiPrintHelper.getInstance().initSunmiPrinterService(this)
        SunmiPrintHelper.getInstance().initPrinter()
        lifecycleScope.launch(Dispatchers.IO) {
            checkStoreClosed()
            delay(20000)
            orderScreenViewModel.soundPoolPlayer.stop()
        }
    }

    private suspend fun checkStoreClosed() {
        orderScreenViewModel.getStoreSettings.invoke().collectLatest { settings ->
            when (settings) {
                is Success -> {
                    withContext(Dispatchers.IO) {
                        withContext(Dispatchers.Main) {
                            loginViewModel.prefs.storeSettings =
                                settings().myStoreSettings?.storeSettings
                        }
                        val nowDate = orderScreenViewModel.trueTimeImpl.now().toGMT()
                        val weekday: String = nowDate.getDayOfWeek()
                        val timing =
                            settings().myStoreSettings?.storeSettings?.timingJson?.firstOrNull {
                                it.day == weekday
                            }
                        val openHour = timing?.openingTime?.split(":")?.get(0)?.toInt() ?: 0
                        val openMinute = timing?.openingTime?.split(":")?.get(1)?.toInt() ?: 0
                        val closeHour = timing?.closingTime?.split(":")?.get(0)?.toInt() ?: 0
                        val closeMinute = timing?.closingTime?.split(":")?.get(1)?.toInt() ?: 0
                        val openDate = getDateFromHourAndMinute(
                            openHour, openMinute, nowDate = nowDate
                        ).toGMT()
                        val closeDate = getDateFromHourAndMinute(
                            closeHour, closeMinute, nowDate = nowDate
                        ).toGMT()
                        orderScreenViewModel.updateStoreTimings(
                            openingTime = formatDateToHourAndMinute(openDate),
                            closeTime = formatDateToHourAndMinute(
                                closeDate
                            )
                        )
                        if (nowDate.after(openDate) && nowDate.before(closeDate)) {
                            orderScreenViewModel.updateStoreCloseSettings(storeCloseSettings = "store_open")
                            loginViewModel.prefs.storeClosed = false
                        } else {
                            orderScreenViewModel.updateStoreCloseSettings(storeCloseSettings = "store_close")
                            loginViewModel.prefs.storeClosed = true
                        }
                    }
                }

                else -> {
                }
            }
        }
    }

    val functionSilentOrders = throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 100) {
        lifecycleScope.launch(Dispatchers.IO) {
            callOrders()
        }
    }

    val functionOpenCloseStore =
        throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 100) {
            lifecycleScope.launch(Dispatchers.IO) {
                checkStoreClosed()
            }
        }

    val function = throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 60000) {
        lifecycleScope.launch(Dispatchers.IO) {
            updateOrderCountMinutes()
        }
    }

    val functionNewOrders =
        throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 2 * 60 * 1000) {
            lifecycleScope.launch(Dispatchers.IO) {
                withContext(Dispatchers.Main) {
                    showDialogWhilePlaying()
                }
            }
        }

    val functionOtherOrders =
        throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 10 * 1000) {
            lifecycleScope.launch(Dispatchers.IO) {
                withContext(Dispatchers.Main) {
                    showDialogWhilePlaying()
                }
            }
        }

    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            // Get extra data included in the Intent
            val message = intent.getStringExtra("message")
            val type = intent.getStringExtra("type")
            if (type == "table_update") {
                val tableUpdateModel = Json.decodeFromString<TableUpdateSocket>(message ?: "")
                if(productsScreenViewModel.prefs.localDeviceId != tableUpdateModel.deviceId) {
                    when (tableUpdateModel.action) {
                        "create", "update" -> {
                            lifecycleScope.launch(Dispatchers.IO) {
                                productsScreenViewModel.syncTableFromSocket(
                                    tableId = tableUpdateModel.tableId ?: -1,
                                    state = productState ?: ProductsScreenState()
                                )
                            }
                        }
                        "remove" -> {
                            if(productState?.selectedTables?.any { it.tableId == tableUpdateModel.tableId } == true){
                                lifecycleScope.launch(Dispatchers.IO) {
                                    productsScreenViewModel.clearCartAndRemoveTable(
                                        currentTableId = tableUpdateModel.tableId,
                                        state = productState ?: ProductsScreenState()
                                    )
                                }
                            }
                        }
                    }
                }
            } else if (type == "reservation_update") {
                // Handle reservation update messages
                lifecycleScope.launch(Dispatchers.IO) {
                    reservationsViewModel.loadActiveReservations()
                    reservationsViewModel.loadAllReservations()
                }
            } else {
//                if (type == "new_order" || type == "cancel_order") {
//                    val title = if (dialogType == "new_order") "New Order" else "Order Cancelled"
//                    orderScreenViewModel.updateShowDialog(
//                        dialogType = type,
//                        dialogMessage = message ?: "",
//                        showDialog = UUID.randomUUID().toString(),
//                        dialogTitle = title,
//                        isScheduleOrder = isScheduleOrder
//                    )
//                } else if (type == "Network") {
//                    if (ordersResponse?.orders?.isNotEmpty() != true) {
//                        val function =
//                            throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 1000) {
//                                lifecycleScope.launch(Dispatchers.IO) {
//                                    callOrders()
//                                }
//                            }
//                        function(Unit)
//                    }
//                } else if (type == "update_orders_loop") {
//                    if (EndlessSocketService.dialogType == "new_order" || EndlessSocketService.dialogType == "cancel_order") {
//                        functionNewOrders(Unit)
//                    } else {
//                        functionOtherOrders(Unit)
//                    }
//                    if (ordersResponse?.orders?.isNotEmpty() == true) {
//                        function(Unit)
//                    }
//                    orderScreenViewModel.updateIsConnected(
//                        isConnected2 = EndlessSocketService.flowIsConnected2.value
//                    )
//                } else if (type == "unknown_status") {
//                    functionSilentOrders(Unit)
//                } else if (type == "update_store_timings") {
//                    functionOpenCloseStore(Unit)
//                } else if (type == "update_order_with_dialog") {
//                    val title = "Update Order"
//                    orderScreenViewModel.updateShowDialog(
//                        dialogType = type,
//                        dialogMessage = message ?: "",
//                        showDialog = UUID.randomUUID().toString(),
//                        dialogTitle = title
//                    )
//                } else if (type == "store_open_close") {
//                    lifecycleScope.launch(Dispatchers.IO) {
//                        orderScreenViewModel.getIsClosed()
//                    }
//                } else {
//                    functionSilentOrders(Unit)
//                }
            }
        }
    }

    fun updateOrderCountMinutes() {
        val orders = ordersResponse?.orders?.map {
            if (it.order?.acceptedDate?.isNotEmpty() == true) {
                val dateFormat =
                    if (it.order.acceptedDate.contains("T")) DATE_FORMAT_BACK_END else DATE_FORMAT_APP
                if (getMinutesBetweenTwoDates(
                        startDateString = it.order.acceptedDate,
                        endDateString = orderScreenViewModel.getCurrentUTC(),
                        formatStart = dateFormat,
                        formatEnd = DATE_FORMAT_APP
                    ) > 0
                ) {
                    val order = it.order.copy(
                        countMinutes = getMinutesBetweenTwoDates(
                            startDateString = it.order.acceptedDate ?: "",
                            endDateString = orderScreenViewModel.getCurrentUTC(),
                            formatStart = dateFormat,
                            formatEnd = DATE_FORMAT_APP
                        ).toInt()
                    )
                    it.copy(order = order)
                } else it
            } else it
        }
        orderScreenViewModel.updateOrders(
            ordersResponse = OrderResponse(
                orders = orders, success = true
            )
        )
    }

    fun showDialogWhilePlaying() {
        if (orderScreenViewModel.audioManager.isMusicActive && orderScreenViewModel.soundPoolPlayer.isPlaying) {
            orderScreenViewModel.updateShowDialog(
                dialogMessage = EndlessSocketService.dialogMessage,
                dialogTitle = when (EndlessSocketService.dialogType) {
                    "new_order" -> "New Order"
                    "cancel_order" -> "Order Cancelled"
                    else -> "Update Order"
                },
                dialogType = EndlessSocketService.dialogType,
                showDialog = UUID.randomUUID().toString()
            )
        } else {
            if (dialog?.isShowing == true && EndlessSocketService.dialogType != "new_order" && EndlessSocketService.dialogType != "cancel_order") {
                lifecycleScope.launch(Dispatchers.IO) {
                    callOrders()
                }
                dismissDialog()
            }
        }

    }

    fun dismissDialog() {
        EndlessSocketService.dialogType = ""
        EndlessSocketService.dialogMessage = ""
        orderScreenViewModel.updateShowDialog(
            dialogMessage = "", dialogTitle = "", dialogType = "", showDialog = ""
        )
    }

    fun showDialog(message: String, title: String, type: String, isScheduleOrder: Boolean = false) {
        if (message.isNotEmpty() && title.isNotEmpty()) {
            dialog = MaterialDialog(this@MainActivity)
            val layout =
                if (type == "new_order") R.layout.dialog_new_order else R.layout.dialog_cancel_order
            dialog?.setContentView(layout)
            val tvTitle = dialog?.findViewById<TextView>(R.id.tvTitle)
            tvTitle?.text = title
            val tvMessage = dialog?.findViewById<TextView>(R.id.tvMessage)
            tvMessage?.text = message
            val btnOk = dialog?.findViewById<TextView>(R.id.btnOk)
            btnOk?.setOnClickListener {
                dialog?.dismiss()
                dismissDialog()
                lifecycleScope.launch(Dispatchers.IO) {
                    withContext(Dispatchers.Main) {
                        orderScreenViewModel.soundPoolPlayer.stop()
                    }
                    callOrders()
                    if (isScheduleOrder) {
                        orderScreenViewModel.updateCurrentRoute("1")
                    } else {
                        orderScreenViewModel.updateCurrentRoute("0")
                    }
                }
            }
            lifecycleScope.launch(Dispatchers.IO) {
                delay(2 * 60 * 1000)
                withContext(Dispatchers.Main) {
                    orderScreenViewModel.soundPoolPlayer.stop(SoundPoolPlayer.streamId)
                    dismissDialog()
                    callOrdersSilent()
                }
            }
            dialog?.cancelable(false)
            dialog?.cancelOnTouchOutside(false)
            dialog?.show()
            orderScreenViewModel.updateDialogType(dialogType = type)
        }
    }

    fun showAutoDismissDialog(message: String, title: String) {
        if (message.isNotEmpty() && title.isNotEmpty()) {
            dialog = MaterialDialog(this@MainActivity)
            val layout = R.layout.dialog_update_order
            dialog?.setContentView(layout)
            val tvTitle = dialog?.findViewById<TextView>(R.id.tvTitle)
            tvTitle?.text = title
            val tvMessage = dialog?.findViewById<TextView>(R.id.tvMessage)
            tvMessage?.text = message
            val tvOrderId = dialog?.findViewById<TextView>(R.id.tvOrderId)
            val btnOk = dialog?.findViewById<TextView>(R.id.btnOk)
            dialog?.cancelable(false)
            btnOk?.setOnClickListener {
                lifecycleScope.launch(Dispatchers.Main) {
                    orderScreenViewModel.soundPoolPlayer.stop(SoundPoolPlayer.streamId)
                    dismissDialog()
                    dialog?.dismiss()
                    callOrdersSilent()
                }
            }
            dialog?.cancelOnTouchOutside(false)
            dialog?.show()
            orderScreenViewModel.updateDialogType(dialogType = "update_order")
            lifecycleScope.launch(Dispatchers.IO) {
                delay(10 * 1000)
                withContext(Dispatchers.Main) {
                    orderScreenViewModel.soundPoolPlayer.stop(SoundPoolPlayer.streamId)
                    dismissDialog()
                    dialog?.dismiss()
                    callOrdersSilent()
                }
            }
        }
    }

    override fun invalidate() {

    }

    override fun onDestroy() {
        // Unregister since the activity is about to be closed.
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver)
//        LocalBroadcastManager.getInstance(this).unregisterReceiver(networkChangeLocalMessageReceiver)
        super.onDestroy()
    }


    @Composable
    fun ScheduleOrdersScreen(innerPadding: PaddingValues) {
        ScheduleOrderScreenTopFunction(
            modifier = Modifier.padding(innerPadding),
            viewModel = orderScreenViewModel,
            onPrintBill = { bitmap ->
                printOrderBitmap(bitmap, this)
            },
            onTrackingUrlClick = { url ->

                val finalUrl = if (!url.startsWith("http://") && !url.startsWith("https://")) {
                    "http://$url"
                } else url

                val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(finalUrl))
                val packageManager: PackageManager = packageManager
                if (browserIntent.resolveActivity(packageManager) != null) {
                    startActivity(browserIntent)
                } else {
                    MaterialDialog(this@MainActivity).show {
                        title(text = "Error")
                        message(text = "No browser found to open the tracking url")
                        positiveButton(text = "Ok") {
                            it.dismiss()
                        }
                    }
                }

            },
            onUpdateShowAllOrders = {
                orderScreenViewModel.updateShowAllOrders(it)
                lifecycleScope.launch(Dispatchers.IO) {
                    orderScreenViewModel.getOrders(isShowAllOrders = it)
                    orderScreenViewModel.getScheduleOrders()
                }
            },
            callOrders = {
                showDialogWhilePlaying()
            })
    }

    @SuppressLint("CheckResult")
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun MyTopAppBar(viewModel: OrderScreenViewModel) {

        val rewardsState by rewardsViewModel.collectAsState()

        TopAppBar(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
                .background(Color.White)
                .shadow(4.dp)
                .drawBehind {
                    val strokeWidth = 1.dp.toPx()
                    drawLine(
                        color = Color.LightGray,
                        start = Offset(0f, size.height - strokeWidth / 2),
                        end = Offset(size.width, size.height - strokeWidth / 2),
                        strokeWidth = strokeWidth
                    )
                },
            title = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Left side - Settings and Refresh buttons
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Settings Icon
                        Box(
                            modifier = Modifier
                                .background(
                                    Color(0xFF2E7D32), shape = CircleShape
                                )
                                .size(40.dp)
                                .clickable {
                                    // Determine SumUp menu item based on login status
                                    val isLoggedIn = SumUpPaymentHelper.isLoggedIn()
                                    Log.d(
                                        "MainActivity",
                                        "Menu creation: SumUp isLoggedIn = $isLoggedIn"
                                    )
                                    val sumUpMenuItem =
                                        if (isLoggedIn) "SumUp Logout" else "SumUp Login"
                                    Log.d(
                                        "MainActivity",
                                        "Menu creation: sumUpMenuItem = $sumUpMenuItem"
                                    )

//                                    // Also show a toast to make it visible without logs
//                                    Toast.makeText(
//                                        this@MainActivity,
//                                        "SumUp Status: $sumUpMenuItem (isLoggedIn: $isLoggedIn)",
//                                        Toast.LENGTH_SHORT
//                                    ).show()

                                    val myItems = if (BuildConfig.DEBUG) listOf(
                                        sumUpMenuItem,
                                        "Rewards",
                                        "Employee Hour Sign In",
                                        "Sales Report",
                                        "Refunds",
                                        "Change Store",
                                        "Api Logs",
                                        "Payment Mocks",
                                        "Logout",
                                        "Upload Logs",
                                        "Go Back"
                                    )
                                    else listOf(
                                        sumUpMenuItem,
                                        "Rewards",
                                        "Employee Hour Sign In",
                                        "Sales Report",
                                        "Refunds",
                                        "Change Store",
                                        "Logout",
                                        "Go Back"
                                    )

                                    Log.d(
                                        "MainActivity",
                                        "Complete menu items: $myItems"
                                    )

                                    dialog = MaterialDialog(this@MainActivity)
                                    dialog?.cancelable(false)
                                    dialog?.cancelOnTouchOutside(false)
                                    dialog?.show {
                                        title(text = "Settings")
                                        listItems(items = myItems) { dialog, index, text ->
                                            when (text) {
                                                "SumUp Login" -> {
                                                    Log.d(
                                                        "MainActivity",
                                                        "SumUp Login clicked"
                                                    )
                                                    dismissDialog()
                                                    try {
                                                        val currentLoginStatus =
                                                            SumUpPaymentHelper.isLoggedIn()
                                                        Log.d(
                                                            "MainActivity",
                                                            "Current SumUp login status: $currentLoginStatus"
                                                        )
                                                        if (currentLoginStatus) {
                                                            Toast.makeText(
                                                                this@MainActivity,
                                                                "Already logged in to SumUp",
                                                                Toast.LENGTH_SHORT
                                                            ).show()
                                                        } else {
                                                            Log.d(
                                                                "MainActivity",
                                                                "Starting SumUp login from menu"
                                                            )
                                                            SumUpPaymentHelper.startLogin(this@MainActivity)
                                                        }
                                                    } catch (e: Exception) {
                                                        Log.e(
                                                            "MainActivity",
                                                            "Error with SumUp login",
                                                            e
                                                        )
                                                        Toast.makeText(
                                                            this@MainActivity,
                                                            "SumUp login not available: ${e.message}",
                                                            Toast.LENGTH_SHORT
                                                        ).show()
                                                    }
                                                }

                                                "SumUp Logout" -> {
                                                    Log.d(
                                                        "MainActivity",
                                                        "SumUp Logout clicked"
                                                    )
                                                    dismissDialog()
                                                    try {
                                                        if (SumUpPaymentHelper.isLoggedIn()) {
                                                            // Show confirmation dialog for logout
                                                            MaterialDialog(this@MainActivity).show {
                                                                title(text = "SumUp Logout")
                                                                message(text = "Are you sure you want to logout from SumUp?")
                                                                positiveButton(text = "Yes") {
                                                                    SumUpPaymentHelper.logout()
                                                                    Toast.makeText(
                                                                        this@MainActivity,
                                                                        "Logged out from SumUp",
                                                                        Toast.LENGTH_SHORT
                                                                    ).show()
                                                                }
                                                                negativeButton(text = "No") {
                                                                    // Do nothing, dialog will dismiss
                                                                }
                                                            }
                                                        } else {
                                                            Toast.makeText(
                                                                this@MainActivity,
                                                                "Not logged in to SumUp",
                                                                Toast.LENGTH_SHORT
                                                            ).show()
                                                        }
                                                    } catch (e: Exception) {
                                                        Toast.makeText(
                                                            this@MainActivity,
                                                            "SumUp logout not available",
                                                            Toast.LENGTH_SHORT
                                                        ).show()
                                                    }
                                                }

                                                "Test SumUp Status" -> {
                                                    dismissDialog()
                                                    try {
                                                        val isLoggedIn =
                                                            SumUpPaymentHelper.isLoggedIn()
                                                        val merchantInfo =
                                                            SumUpPaymentHelper.getCurrentMerchantInfo()
                                                        Log.d(
                                                            "MainActivity",
                                                            "SumUp Status Test: isLoggedIn = $isLoggedIn, merchantInfo = $merchantInfo"
                                                        )
                                                        Toast.makeText(
                                                            this@MainActivity,
                                                            "SumUp Status: Logged in = $isLoggedIn\nMerchant: $merchantInfo",
                                                            Toast.LENGTH_LONG
                                                        ).show()
                                                    } catch (e: Exception) {
                                                        Log.e(
                                                            "MainActivity",
                                                            "Error checking SumUp status",
                                                            e
                                                        )
                                                        Toast.makeText(
                                                            this@MainActivity,
                                                            "Error checking SumUp status: ${e.message}",
                                                            Toast.LENGTH_LONG
                                                        ).show()
                                                    }
                                                }

                                                "Logout" -> {
                                                    //are you sure you want to logout?
                                                    MaterialDialog(this@MainActivity).show {
                                                        title(text = "Logout")
                                                        message(text = "Are you sure you want to logout?")
                                                        positiveButton(text = "Yes") {
                                                            viewModel.prefs.loginResponse = null
                                                            viewModel.prefs.store = null
                                                            dismissDialog()
                                                            val intent = Intent(
                                                                this@MainActivity,
                                                                LoginActivity::class.java
                                                            )
                                                            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                                                            startActivity(intent)
                                                        }
                                                        negativeButton(text = "No") {
                                                            dismissDialog()
                                                        }
                                                    }
                                                }

                                                "Change Store" -> {
                                                    viewModel.prefs.store = null
                                                    dismissDialog()
                                                    val intent = Intent(
                                                        this@MainActivity,
                                                        SelectStoreActivity::class.java
                                                    )
                                                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                                                    startActivity(intent)
                                                }

                                                "Stock Management" -> {
                                                    dismissDialog()
                                                    val intent = Intent(
                                                        this@MainActivity, StockActivity::class.java
                                                    )
                                                    startActivity(intent)
                                                }

                                                "Go Back" -> {
                                                    dismissDialog()
                                                }

                                                "Api Logs" -> {
                                                    Pluto.open(PlutoNetworkPlugin.ID)
                                                    dismissDialog()
                                                }

                                                "Sales Report" -> {
                                                    dismissDialog()
                                                    val intent = Intent(
                                                        this@MainActivity, SalesActivity::class.java
                                                    )
                                                    startActivity(intent)
                                                }

                                                "Payment Mocks" -> {
                                                    MavericksLauncherActivity.show(context = this@MainActivity)
                                                    dismissDialog()
                                                }

                                                "Refunds" -> {
                                                    dismissDialog()
                                                    if (paymentViewModel.isSumUp()) {
                                                        val intent = Intent(
                                                            this@MainActivity,
                                                            RefundSumUpActivity::class.java
                                                        )
                                                        startActivity(intent)
                                                    } else {
                                                        val intent = Intent(
                                                            this@MainActivity,
                                                            GuavaOrdersActivity::class.java
                                                        )
                                                        startActivity(intent)
                                                    }
                                                }

                                                "Employee Hour Sign In" -> {
                                                    dismissDialog()
                                                    val intent = Intent(
                                                        this@MainActivity,
                                                        SelectUserProfileActivity::class.java
                                                    )
                                                    startActivity(intent)
                                                }

                                                "Upload Logs" -> {
                                                    dismissDialog()
                                                    LogUploadManager.uploadLogsNow(this@MainActivity)
                                                    Toast.makeText(
                                                        this@MainActivity,
                                                        "Log upload requested",
                                                        Toast.LENGTH_SHORT
                                                    ).show()
                                                }

                                                "Local Orders" -> {
                                                    dismissDialog()
                                                    val intent = Intent(
                                                        this@MainActivity,
                                                        LocalOrdersActivity::class.java
                                                    )
                                                    startActivity(intent)
                                                }

                                                "Rewards" -> {
                                                    dismissDialog()
                                                    if (rewardsState.selectedCustomer == null &&
                                                        rewardsState.customerIdInput.isEmpty() &&
                                                        !rewardsState.isLoading
                                                    ) {
                                                        startQRCodeScan()
                                                    } else {
                                                        rewardsViewModel.showRewardsDialog(show = true)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Image(
                                imageVector = Icons.Filled.Settings,
                                colorFilter = ColorFilter.tint(color = Color.White),
                                contentDescription = "Settings",
                                modifier = Modifier.size(24.dp)
                            )
                        }

                        // Refresh Button (Right of Settings)
                        var isRefreshing by remember { mutableStateOf(false) }

                        IconButton(
                            onClick = {
                                if (!isRefreshing) {
                                    // Call login use case to refresh authentication
                                    lifecycleScope.launch {
                                        isRefreshing = true
                                        try {
                                            val email =
                                                viewModel.prefs.loginResponse?.user?.email ?: ""
                                            val password = viewModel.prefs.password
                                            if (email.isNotEmpty()) {
                                                loginViewModel.login(
                                                    email,
                                                    password,
                                                    shouldEncrypt = true
                                                ).collectLatest { response ->
                                                    when (response) {
                                                        is Loading<*> -> {
                                                            isRefreshing = true
                                                        }

                                                        is Success -> {
                                                            isRefreshing = false
                                                            commonViewModel.updateShowSuccessDialog(
                                                                true
                                                            )
                                                            commonViewModel.updateSuccessDialogMessage(
                                                                "Refreshed Successfully"
                                                            )
                                                        }

                                                        is Fail -> {
                                                            isRefreshing = false
                                                            Toast.makeText(
                                                                this@MainActivity,
                                                                "Failed to refresh",
                                                                Toast.LENGTH_SHORT
                                                            ).show()
                                                        }

                                                        else -> {
                                                            isRefreshing = false
                                                        }
                                                    }
                                                }
                                            } else {
                                                isRefreshing = false
                                            }
                                        } catch (e: Exception) {
                                            isRefreshing = false
                                            Toast.makeText(
                                                this@MainActivity,
                                                e.message ?: "Unknown error",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        } finally {
                                            isRefreshing = false
                                        }
                                    }
                                }
                            },
                            enabled = !isRefreshing,
                            modifier = Modifier
                                .background(
                                    Color(0xFF2E7D32),
                                    shape = CircleShape
                                )
                                .size(40.dp)
                        ) {
                            if (isRefreshing) {
                                CircularProgressIndicator(
                                    color = Color.White,
                                    strokeWidth = 2.dp,
                                    modifier = Modifier.size(20.dp)
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.Filled.Refresh,
                                    contentDescription = "Refresh",
                                    tint = Color.White,
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                        }
                    }

                    // Center Content - Store Branding
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        val store = viewModel.prefs.store
                        val imageUrl = "${BASE_DOMAIN}/dasa/streamer?name=${store?.banner}"
                        val request: ImageRequest =
                            ImageRequest.Builder(LocalContext.current.applicationContext)
                                .data(imageUrl)
                                .crossfade(true).diskCacheKey(imageUrl)
                                .diskCachePolicy(CachePolicy.ENABLED)
                                .setHeader("Cache-Control", "max-age=31536000").build()
                        AsyncImage(
                            modifier = Modifier.size(50.dp),
                            model = request,
                            contentDescription = ""
                        )
                        if ((viewModel.prefs.loginResponse?.stores?.size ?: 0) > 1) {
                            Text(
                                text = store?.name ?: "Store Name",
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Normal,
                                    color = Color.Black
                                )
                            )
                        }
                    }

                    // Right side - Empty space for balance
                    Box(
                        modifier = Modifier.size(40.dp)
                    )
                }
            }
        )
    }

    fun callOrders() {
        lifecycleScope.launch(Dispatchers.IO) {
            orderScreenViewModel.getOrders(isShowAllOrders = isShowAllOrders)
        }
        lifecycleScope.launch(Dispatchers.IO) {
            orderScreenViewModel.getScheduleOrders()
        }
    }

    fun callOrdersSilent() {
        lifecycleScope.launch(Dispatchers.IO) {
            orderScreenViewModel.getOrdersSilent(isShowAllOrders = isShowAllOrders)
        }
        lifecycleScope.launch(Dispatchers.IO) {
            orderScreenViewModel.getScheduleOrdersSilent()
        }
    }

    private fun initPrinter() {
        this.let { printerViewModel.initPrinter(it) }
        printerViewModel.showPrinters.observe(this, Observer {
            if (selectPrinter != null) {
                lcdViewModel.lcdCtrl()
            }
        })
    }

    /*override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == 129 && grantResults.isNotEmpty() && grantResults[0] != PackageManager.PERMISSION_GRANTED) {
            MaterialDialog(this).show {
                title(text = "Permission Denied")
                message(text = "Please allow location permission to continue")
            }
        }
    }*/


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // Handle SumUp activity results
        when (requestCode) {
            SumUpPaymentHelper.REQUEST_CODE_SUMUP_LOGIN -> {
                handleSumUpLoginResult(data)
            }

            SumUpPaymentHelper.REQUEST_CODE_SUMUP_PAYMENT -> {
                handleSumUpPaymentResult(data)
            }
        }
    }

    private fun handleSumUpLoginResult(data: Intent?) {
        Log.d("MainActivity", "handleSumUpLoginResult called")
        if (data != null) {
            val extra = data.extras
            val resultCode =
                extra?.getInt(SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(SumUpAPI.Response.MESSAGE)
                ?: "Unknown error"

            val success =
                resultCode == SumUpAPI.Response.ResultCode.SUCCESSFUL
            Log.d(
                "MainActivity",
                "SumUp login result: success = $success, resultCode = $resultCode"
            )

            if (success) {
                Toast.makeText(this, "SumUp login successful", Toast.LENGTH_SHORT).show()
                Log.d(
                    "MainActivity",
                    "SumUp login successful - menu will update on next open"
                )

                // Auto-start payment if there's an active order
                currentSumUpOrder?.let { order ->
                    SumUpPaymentHelper.startPayment(this, order)
                }
            } else {
                Toast.makeText(this, "SumUp login failed: $message", Toast.LENGTH_LONG).show()
            }
        } else {
            Toast.makeText(this, "SumUp login cancelled", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleSumUpPaymentResult(data: Intent?) {
        if (data != null) {
            val extra = data.extras
            val resultCode =
                extra?.getInt(SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(SumUpAPI.Response.MESSAGE)
                ?: "Unknown error"
            val transactionInfo =
                extra?.getParcelable<TransactionInfo>(SumUpAPI.Response.TX_INFO)

            val success =
                resultCode == SumUpAPI.Response.ResultCode.SUCCESSFUL
            if (success && transactionInfo != null) {
                Toast.makeText(this, "Payment successful! Processing order...", Toast.LENGTH_SHORT)
                    .show()

                // Handle successful payment - save order to server
                currentSumUpOrder?.let { order ->
                    // Update order with transaction info
                    val updatedOrder = order.copy(
                        transactionId = transactionInfo.transactionCode ?: "",
                        paymentType = 6 // Card payment type
                    )
                    handleCashPaymentCompleteGuava(updatedOrder)
                }
            } else {
                Toast.makeText(this, "Payment failed: $message", Toast.LENGTH_LONG).show()
            }
        } else {
            Toast.makeText(this, "Payment cancelled", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleCardPayment(order: Order, state: ProductsScreenState) {
        val updatedOrder = order.copy(paymentType = 6,
            totalPrice = order.totalPrice(
                applyServiceCharge = state.serviceChargeApplied,
                serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
            ) ?: 0.0
        )
        if (paymentViewModel.isSumUp()) {
            // Store order for auto-payment after login
            currentSumUpOrder = updatedOrder

            // Direct SumUp login/payment flow - no intermediate activity
            if (SumUpPaymentHelper.isLoggedIn()) {
                // Already logged in, start payment directly
                SumUpPaymentHelper.startPayment(
                    this@MainActivity,
                    updatedOrder
                )
            } else {
                // Not logged in, start login first
                SumUpPaymentHelper.startLogin(this@MainActivity)
            }
        } else { // Place order directly
            lifecycleScope.launch {
                productsScreenViewModel.placeOrder(
                    order = updatedOrder,
                    transId = "",
                    state = state
                ).collectLatest {
                    if (it is Success) {
                        lifecycleScope.launch(Dispatchers.Main) {
                            withContext(Dispatchers.IO) {
                                productsScreenViewModel.clearCartAndRemoveTable(
                                    state = state,
                                    currentTableId = state.getCurrentTableId()
                                )
                                productsScreenViewModel.updateShowPrintingPreview(
                                    order = OrderItem2(
                                        customer = Customer(
                                            name = "Walk-in Customer (In Store)",
                                            phone = ""
                                        ),
                                        order = it().order!!
                                    )
                                )
                            }
                            productsScreenViewModel.updateCartVisibility(
                                false
                            )

                            commonViewModel.updateSuccessAvailDialogMessage(
                                "Transaction Completed"
                            )
                            commonViewModel.updateShowSuccessAvailDialog(
                                show = true
                            )
                        }
                    }
                }
            }
        }
    }

    private fun handleCashPaymentCompleteGuava(finalOrder: Order) {
        lifecycleScope.launch {
            productsScreenViewModel.placeOrder(
                order = finalOrder,
                transId = finalOrder.transactionId ?: UUID.randomUUID().toString(),
                state = productState ?: ProductsScreenState()
            ).collectLatest {
                if (it is Success) {
                    productsScreenViewModel.clearCartAndRemoveTable(
                        state = productState ?: ProductsScreenState(),
                        currentTableId = productState?.getCurrentTableId()
                    )
                    withContext(Dispatchers.Main) {
                        if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                            selectPrinter?.cashDrawerApi()?.open(null)
                        }
                        commonViewModel.updateSuccessAvailDialogMessage(
                            "Transaction Completed"
                        )
                        commonViewModel.updateShowSuccessAvailDialog(
                            show = true
                        )
                    }
                    launch {
                        productsScreenViewModel.updateShowPrintingPreview(
                            order = OrderItem2(
                                customer = Customer(
                                    name = "Walk-in Customer (In Store)",
                                    phone = ""
                                ), order = it().order!!
                            )
                        )
                        productsScreenViewModel.updateCartVisibility(false)
                    }
                }
            }
        }
    }
}

fun createBitmapFromPicture(picture: Picture): Bitmap {
    val bitmap = Bitmap.createBitmap(
        picture.width, picture.height, Bitmap.Config.ARGB_8888
    )

    val canvas = Canvas(bitmap)
    canvas.drawColor(android.graphics.Color.WHITE)
    canvas.drawPicture(picture)
    return bitmap
}

@Composable
fun SelectedCustomerBanner(
    customerName: String,
    onRemove: () -> Unit
) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.3f)
                .align(Alignment.TopCenter)
                .padding(top = 60.dp),
            shape = RoundedCornerShape(12.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Customer Selected: ",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black
                    )
                    Text(
                        text = customerName,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                }

                IconButton(
                    onClick = onRemove,
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            color = ThemeGreen.copy(alpha = 0.1f),
                            shape = CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Remove selected customer",
                        tint = ThemeGreen,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}
